import {
  users,
  products,
  categories,
  locations,
  conversations,
  messages,
  blogPosts,
  favorites,
  type User,
  type UpsertUser,
  type Product,
  type InsertProduct,
  type ProductWithDetails,
  type Category,
  type InsertCategory,
  type Location,
  type InsertLocation,
  type Conversation,
  type InsertConversation,
  type ConversationWithDetails,
  type Message,
  type InsertMessage,
  type MessageWithSender,
  type BlogPost,
  type InsertBlogPost,
  type BlogPostWithAuthor,
  type Favorite,
} from "@shared/schema-sqlite";
import { db } from "./db";
import { eq, desc, and, or, like, sql, ilike, count } from "drizzle-orm";
import crypto from "crypto";

export interface IStorage {
  // User operations (mandatory for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  updateUserStatus(id: string, lastSeen: Date): Promise<void>;
  verifyUser(id: string): Promise<void>;
  makeAdmin(id: string): Promise<void>;
  getAllUsers(): Promise<User[]>;

  // Authentication operations
  getUserByPhoneOrEmail(phone: string, email?: string): Promise<User | undefined>;
  updateUserLastSeen(id: string): Promise<void>;

  // Product operations
  createProduct(product: InsertProduct, sellerId: string): Promise<Product>;
  getProduct(id: string): Promise<ProductWithDetails | undefined>;
  updateProduct(id: string, updates: Partial<InsertProduct>): Promise<Product>;
  deleteProduct(id: string): Promise<void>;
  getProducts(filters?: {
    categoryId?: number;
    locationId?: number;
    minPrice?: number;
    maxPrice?: number;
    condition?: string;
    search?: string;
    sellerId?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ products: ProductWithDetails[]; total: number }>;
  incrementProductViews(id: string): Promise<void>;

  // Category operations
  createCategory(category: InsertCategory): Promise<Category>;
  getCategories(): Promise<Category[]>;
  getCategory(id: number): Promise<Category | undefined>;

  // Location operations
  createLocation(location: InsertLocation): Promise<Location>;
  getLocations(): Promise<Location[]>;
  getLocation(id: number): Promise<Location | undefined>;

  // Conversation operations
  createConversation(conversation: InsertConversation): Promise<Conversation>;
  getConversation(id: string): Promise<ConversationWithDetails | undefined>;
  getUserConversations(userId: string): Promise<ConversationWithDetails[]>;
  findConversation(productId: string, buyerId: string, sellerId: string): Promise<Conversation | undefined>;

  // Message operations
  createMessage(message: InsertMessage): Promise<Message>;
  getMessages(conversationId: string): Promise<MessageWithSender[]>;
  markMessagesAsRead(conversationId: string, userId: string): Promise<void>;
  getUnreadMessageCount(userId: string): Promise<number>;

  // Blog operations
  createBlogPost(post: InsertBlogPost, authorId: string): Promise<BlogPost>;
  updateBlogPost(id: string, updates: Partial<InsertBlogPost>): Promise<BlogPost>;
  deleteBlogPost(id: string): Promise<void>;
  getBlogPosts(published?: boolean): Promise<BlogPostWithAuthor[]>;
  getBlogPost(id: string): Promise<BlogPostWithAuthor | undefined>;
  publishBlogPost(id: string): Promise<BlogPost>;

  // Favorites operations
  addToFavorites(userId: string, productId: string): Promise<Favorite>;
  removeFromFavorites(userId: string, productId: string): Promise<void>;
  getFavorites(userId: string): Promise<ProductWithDetails[]>;
  isFavorite(userId: string, productId: string): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async updateUserStatus(id: string, lastSeen: Date): Promise<void> {
    await db.update(users).set({ lastSeen }).where(eq(users.id, id));
  }

  async verifyUser(id: string): Promise<void> {
    await db.update(users).set({ isVerified: true }).where(eq(users.id, id));
  }

  async makeAdmin(id: string): Promise<void> {
    await db.update(users).set({ isAdmin: true }).where(eq(users.id, id));
  }

  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(desc(users.createdAt));
  }

  // Authentication operations
  async getUserByPhoneOrEmail(phoneOrEmail: string, email?: string): Promise<User | undefined> {
    // Check if the input looks like an email
    const isEmail = phoneOrEmail.includes('@');

    let conditions;
    if (isEmail) {
      conditions = [eq(users.email, phoneOrEmail)];
    } else {
      conditions = [eq(users.phone, phoneOrEmail)];
    }

    // If email is provided separately, also check for it
    if (email && !isEmail) {
      conditions.push(eq(users.email, email));
    }

    const [user] = await db
      .select()
      .from(users)
      .where(or(...conditions));

    return user;
  }

  async updateUserLastSeen(id: string): Promise<void> {
    await db
      .update(users)
      .set({ lastSeen: new Date() })
      .where(eq(users.id, id));
  }

  // Product operations
  async createProduct(product: InsertProduct, sellerId: string): Promise<Product> {
    const productData = {
      id: crypto.randomUUID(),
      title: product.title,
      description: product.description,
      price: product.price,
      condition: product.condition,
      categoryId: product.categoryId,
      locationId: product.locationId,
      sellerId: sellerId,
      images: typeof product.images === 'string' ? product.images : JSON.stringify(product.images || []),
      contactPhone: product.contactPhone,
      isActive: true,
      isSold: false,
      viewCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const [newProduct] = await db
      .insert(products)
      .values(productData)
      .returning();
    return newProduct;
  }

  async getProduct(id: string): Promise<ProductWithDetails | undefined> {
    const [product] = await db
      .select()
      .from(products)
      .leftJoin(users, eq(products.sellerId, users.id))
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .leftJoin(locations, eq(products.locationId, locations.id))
      .where(and(eq(products.id, id), eq(products.isActive, true)));

    if (!product || !product.users || !product.categories || !product.locations) {
      return undefined;
    }

    return {
      ...product.products,
      seller: product.users,
      category: product.categories,
      location: product.locations,
    };
  }

  async updateProduct(id: string, updates: Partial<InsertProduct>): Promise<Product> {
    const [product] = await db
      .update(products)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(products.id, id))
      .returning();
    return product;
  }

  async deleteProduct(id: string): Promise<void> {
    await db.update(products).set({ isActive: false }).where(eq(products.id, id));
  }

  async getProducts(filters: {
    categoryId?: number;
    locationId?: number;
    minPrice?: number;
    maxPrice?: number;
    condition?: string;
    search?: string;
    sellerId?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ products: ProductWithDetails[]; total: number }> {
    const {
      categoryId,
      locationId,
      minPrice,
      maxPrice,
      condition,
      search,
      sellerId,
      limit = 20,
      offset = 0,
    } = filters;

    let conditions = [eq(products.isActive, true)];

    if (categoryId) {
      conditions.push(eq(products.categoryId, categoryId));
    }
    if (locationId) {
      conditions.push(eq(products.locationId, locationId));
    }
    if (minPrice) {
      conditions.push(sql`${products.price} >= ${minPrice}`);
    }
    if (maxPrice) {
      conditions.push(sql`${products.price} <= ${maxPrice}`);
    }
    if (condition) {
      conditions.push(eq(products.condition, condition));
    }
    if (search) {
      conditions.push(
        or(
          ilike(products.title, `%${search}%`),
          ilike(products.description, `%${search}%`)
        )!
      );
    }
    if (sellerId) {
      conditions.push(eq(products.sellerId, sellerId));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : (conditions.length === 1 ? conditions[0] : undefined);

    const totalQuery = db
      .select({ count: count() })
      .from(products);
    
    if (whereClause) {
      totalQuery.where(whereClause);
    }
    
    const [totalResult] = await totalQuery;

    const resultsQuery = db
      .select()
      .from(products)
      .leftJoin(users, eq(products.sellerId, users.id))
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .leftJoin(locations, eq(products.locationId, locations.id));
    
    if (whereClause) {
      resultsQuery.where(whereClause);
    }
    
    const results = await resultsQuery
      .orderBy(desc(products.createdAt))
      .limit(limit)
      .offset(offset);

    const productsWithDetails = results
      .filter(result => result.users && result.categories && result.locations)
      .map(result => ({
        ...result.products,
        seller: result.users!,
        category: result.categories!,
        location: result.locations!,
      }));

    return {
      products: productsWithDetails,
      total: totalResult.count,
    };
  }

  async incrementProductViews(id: string): Promise<void> {
    await db
      .update(products)
      .set({ viewCount: sql`${products.viewCount} + 1` })
      .where(eq(products.id, id));
  }

  // Category operations
  async createCategory(category: InsertCategory): Promise<Category> {
    const [newCategory] = await db.insert(categories).values(category).returning();
    return newCategory;
  }

  async getCategories(): Promise<Category[]> {
    return await db.select().from(categories).where(eq(categories.isActive, true));
  }

  async getCategory(id: number): Promise<Category | undefined> {
    const [category] = await db.select().from(categories).where(eq(categories.id, id));
    return category;
  }

  // Location operations
  async createLocation(location: InsertLocation): Promise<Location> {
    const [newLocation] = await db.insert(locations).values(location).returning();
    return newLocation;
  }

  async getLocations(): Promise<Location[]> {
    return await db.select().from(locations).where(eq(locations.isActive, true));
  }

  async getLocation(id: number): Promise<Location | undefined> {
    const [location] = await db.select().from(locations).where(eq(locations.id, id));
    return location;
  }

  // Conversation operations
  async createConversation(conversation: InsertConversation): Promise<Conversation> {
    const [newConversation] = await db.insert(conversations).values(conversation).returning();
    return newConversation;
  }

  async getConversation(id: string): Promise<ConversationWithDetails | undefined> {
    const [conversation] = await db
      .select()
      .from(conversations)
      .leftJoin(products, eq(conversations.productId, products.id))
      .leftJoin(users, eq(conversations.buyerId, users.id))
      .where(eq(conversations.id, id));

    if (!conversation || !conversation.products || !conversation.users) {
      return undefined;
    }

    const seller = await this.getUser(conversation.conversations.sellerId);
    const conversationMessages = await this.getMessages(id);

    return {
      ...conversation.conversations,
      product: conversation.products,
      buyer: conversation.users,
      seller: seller!,
      messages: conversationMessages,
    };
  }

  async getUserConversations(userId: string): Promise<ConversationWithDetails[]> {
    const results = await db
      .select()
      .from(conversations)
      .leftJoin(products, eq(conversations.productId, products.id))
      .leftJoin(users, eq(conversations.buyerId, users.id))
      .where(or(eq(conversations.buyerId, userId), eq(conversations.sellerId, userId)))
      .orderBy(desc(conversations.lastMessageAt));

    const conversationsWithDetails = [];
    for (const result of results) {
      if (result.products && result.users) {
        const seller = await this.getUser(result.conversations.sellerId);
        const conversationMessages = await this.getMessages(result.conversations.id);
        
        conversationsWithDetails.push({
          ...result.conversations,
          product: result.products,
          buyer: result.users,
          seller: seller!,
          messages: conversationMessages,
        });
      }
    }

    return conversationsWithDetails;
  }

  async findConversation(productId: string, buyerId: string, sellerId: string): Promise<Conversation | undefined> {
    const [conversation] = await db
      .select()
      .from(conversations)
      .where(
        and(
          eq(conversations.productId, productId),
          eq(conversations.buyerId, buyerId),
          eq(conversations.sellerId, sellerId)
        )
      );
    return conversation;
  }

  // Message operations
  async createMessage(message: InsertMessage): Promise<Message> {
    const [newMessage] = await db.insert(messages).values(message).returning();
    
    // Update conversation last message time
    await db
      .update(conversations)
      .set({ lastMessageAt: new Date() })
      .where(eq(conversations.id, message.conversationId));

    return newMessage;
  }

  async getMessages(conversationId: string): Promise<MessageWithSender[]> {
    const results = await db
      .select()
      .from(messages)
      .leftJoin(users, eq(messages.senderId, users.id))
      .where(eq(messages.conversationId, conversationId))
      .orderBy(messages.createdAt);

    return results
      .filter(result => result.users)
      .map(result => ({
        ...result.messages,
        sender: result.users!,
      }));
  }

  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    await db
      .update(messages)
      .set({ isRead: true })
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(messages.senderId, userId),
          eq(messages.isRead, false)
        )
      );
  }

  async getUnreadMessageCount(userId: string): Promise<number> {
    const userConversations = await db
      .select({ id: conversations.id })
      .from(conversations)
      .where(or(eq(conversations.buyerId, userId), eq(conversations.sellerId, userId)));

    const conversationIds = userConversations.map(c => c.id);

    if (conversationIds.length === 0) return 0;

    const [result] = await db
      .select({ count: count() })
      .from(messages)
      .where(
        and(
          sql`${messages.conversationId} = ANY(${conversationIds})`,
          sql`${messages.senderId} != ${userId}`,
          eq(messages.isRead, false)
        )
      );

    return result.count;
  }

  // Blog operations
  async createBlogPost(post: InsertBlogPost, authorId: string): Promise<BlogPost> {
    const [newPost] = await db
      .insert(blogPosts)
      .values({ ...post, authorId })
      .returning();
    return newPost;
  }

  async updateBlogPost(id: string, updates: Partial<InsertBlogPost>): Promise<BlogPost> {
    const [post] = await db
      .update(blogPosts)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(blogPosts.id, id))
      .returning();
    return post;
  }

  async deleteBlogPost(id: string): Promise<void> {
    await db.delete(blogPosts).where(eq(blogPosts.id, id));
  }

  async getBlogPosts(published?: boolean): Promise<BlogPostWithAuthor[]> {
    let whereCondition = sql`1=1`;
    if (published !== undefined) {
      whereCondition = eq(blogPosts.isPublished, published);
    }

    const results = await db
      .select()
      .from(blogPosts)
      .leftJoin(users, eq(blogPosts.authorId, users.id))
      .where(whereCondition)
      .orderBy(desc(blogPosts.createdAt));

    return results
      .filter(result => result.users)
      .map(result => ({
        ...result.blog_posts,
        author: result.users!,
      }));
  }

  async getBlogPost(id: string): Promise<BlogPostWithAuthor | undefined> {
    const [result] = await db
      .select()
      .from(blogPosts)
      .leftJoin(users, eq(blogPosts.authorId, users.id))
      .where(eq(blogPosts.id, id));

    if (!result || !result.users) {
      return undefined;
    }

    return {
      ...result.blog_posts,
      author: result.users,
    };
  }

  async publishBlogPost(id: string): Promise<BlogPost> {
    const [post] = await db
      .update(blogPosts)
      .set({ isPublished: true, publishedAt: new Date() })
      .where(eq(blogPosts.id, id))
      .returning();
    return post;
  }

  // Favorites operations
  async addToFavorites(userId: string, productId: string): Promise<Favorite> {
    const [favorite] = await db
      .insert(favorites)
      .values({ userId, productId })
      .returning();
    return favorite;
  }

  async removeFromFavorites(userId: string, productId: string): Promise<void> {
    await db
      .delete(favorites)
      .where(and(eq(favorites.userId, userId), eq(favorites.productId, productId)));
  }

  async getFavorites(userId: string): Promise<ProductWithDetails[]> {
    const results = await db
      .select()
      .from(favorites)
      .leftJoin(products, eq(favorites.productId, products.id))
      .leftJoin(users, eq(products.sellerId, users.id))
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .leftJoin(locations, eq(products.locationId, locations.id))
      .where(eq(favorites.userId, userId))
      .orderBy(desc(favorites.createdAt));

    return results
      .filter(result => result.products && result.users && result.categories && result.locations)
      .map(result => ({
        ...result.products!,
        seller: result.users!,
        category: result.categories!,
        location: result.locations!,
      }));
  }

  async isFavorite(userId: string, productId: string): Promise<boolean> {
    const [favorite] = await db
      .select()
      .from(favorites)
      .where(and(eq(favorites.userId, userId), eq(favorites.productId, productId)));
    return !!favorite;
  }
}

export const storage = new DatabaseStorage();
