import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Store, Search, MessageSquare, Plus, User, Settings, LogOut, Shield } from "lucide-react";

interface HeaderProps {
  onOpenChat: () => void;
  onOpenPostModal: () => void;
  onSearch: (query: string) => void;
  onLocationChange: (locationId: string) => void;
}

export default function Header({ onOpenChat, onOpenPostModal, onSearch, onLocationChange }: HeaderProps) {
  const { user, isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");

  const { data: locations } = useQuery({
    queryKey: ['/api/locations'],
  });

  const { data: unreadCount } = useQuery({
    queryKey: ['/api/messages/unread-count'],
    refetchInterval: 30000, // Poll every 30 seconds
  });

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5; // Consider online if last seen within 5 minutes
  };

  return (
    <header className="bg-card shadow-md sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/">
            <div className="flex items-center cursor-pointer">
              <Store className="h-8 w-8 text-primary mr-2" />
              <div className="text-2xl font-bold text-primary">JashoreSellBazar</div>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6 ml-8">
            <Link href="/home">
              <Button variant="ghost" className="text-foreground hover:text-primary">
                হোম
              </Button>
            </Link>
            <Link href="/products">
              <Button variant="ghost" className="text-foreground hover:text-primary">
                সকল পণ্য
              </Button>
            </Link>
            <Link href="/blog">
              <Button variant="ghost" className="text-foreground hover:text-primary">
                ব্লগ
              </Button>
            </Link>
          </nav>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-muted-foreground" />
              </div>
              <Input
                type="text"
                placeholder="পণ্য, বিভাগ বা এলাকায় খুঁজুন..."
                className="pl-10 pr-3"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <div className="absolute inset-y-0 right-0 flex items-center">
                <Select onValueChange={onLocationChange}>
                  <SelectTrigger className="h-full rounded-l-none border-l border-border bg-background px-3 text-sm focus:ring-0">
                    <SelectValue placeholder="সব জেলা" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">সব জেলা</SelectItem>
                    {locations?.map((location: any) => (
                      <SelectItem key={location.id} value={location.id.toString()}>
                        {location.districtBn}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </form>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* Messages */}
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onOpenChat}
                    className="relative p-2"
                  >
                    <MessageSquare className="h-5 w-5" />
                    {unreadCount?.count > 0 && (
                      <Badge className="absolute -top-1 -right-1 bg-secondary text-secondary-foreground text-xs min-w-[1.25rem] h-5 flex items-center justify-center rounded-full">
                        {unreadCount.count > 99 ? '99+' : unreadCount.count}
                      </Badge>
                    )}
                  </Button>
                </div>

                {/* Post Ad Button */}
                <Button
                  onClick={onOpenPostModal}
                  className="bg-secondary hover:bg-secondary/90 text-secondary-foreground"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  বিজ্ঞাপন দিন
                </Button>

                {/* Profile Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-auto px-2">
                      <div className="flex items-center space-x-2">
                        <div className="relative">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={user?.profileImageUrl || undefined}
                              alt={`${user?.firstName} ${user?.lastName}`}
                              className="object-cover"
                            />
                            <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                              {getInitials(user?.firstName, user?.lastName)}
                            </AvatarFallback>
                          </Avatar>
                          {/* Online status indicator */}
                          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${
                            isOnline(user?.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                          }`} />
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-medium">
                            {user?.firstName || 'ব্যবহারকারী'}
                          </span>
                          {user?.isVerified && (
                            <Shield className="h-4 w-4 text-success" title="যাচাইকৃত ব্যবহারকারী" />
                          )}
                        </div>
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <Link href="/profile">
                      <DropdownMenuItem className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        প্রোফাইল
                      </DropdownMenuItem>
                    </Link>
                    <Link href="/favorites">
                      <DropdownMenuItem className="cursor-pointer">
                        <span className="mr-2">❤️</span>
                        পছন্দের তালিকা
                      </DropdownMenuItem>
                    </Link>
                    <Link href="/my-products">
                      <DropdownMenuItem className="cursor-pointer">
                        <Store className="mr-2 h-4 w-4" />
                        আমার বিজ্ঞাপন
                      </DropdownMenuItem>
                    </Link>
                    {user?.isAdmin && (
                      <Link href="/admin">
                        <DropdownMenuItem className="cursor-pointer">
                          <Settings className="mr-2 h-4 w-4" />
                          অ্যাডমিন প্যানেল
                        </DropdownMenuItem>
                      </Link>
                    )}
                    <DropdownMenuItem
                      className="cursor-pointer text-destructive focus:text-destructive"
                      onClick={() => window.location.href = '/api/logout'}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      লগআউট
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              /* Login Button for non-authenticated users */
              <Button
                onClick={() => window.location.href = '/login'}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <User className="h-4 w-4 mr-2" />
                লগইন
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
